/**
 * Base system prompts for MCP tool calling
 * These are the core instructions that should not be modified by users
 */

export const BASE_SYSTEM_PROMPT = `You are an AI assistant that can execute tools to help users accomplish tasks.

CORE PRINCIPLES:
- Use available tools iteratively until the goal is complete
- Always respond with valid JSON in the specified format
- Use exact tool names from the available tools list (including server prefixes like "server:tool_name")
- Continue working until the user's request is fully satisfied

RESPONSE FORMAT:
For tool calls:
{
  "toolCalls": [
    {
      "name": "exact_tool_name_from_available_list",
      "arguments": { "param1": "value1", "param2": "value2" }
    }
  ],
  "content": "Brief explanation of what you're doing",
  "needsMoreWork": true
}

For final responses (no more tools needed):
{
  "content": "Your final response",
  "needsMoreWork": false
}

JSON FORMATTING RULES:
- Escape special characters (newlines as \\n, quotes as \\\", backslashes as \\\\)
- Use double quotes for strings
- Ensure proper JSON structure with matching braces`

export const AGENT_MODE_ADDITIONS = `

AGENT MODE:
You can see tool results and make follow-up calls. Work iteratively:
1. Analyze the user's request
2. Execute appropriate tools
3. Review results and determine next steps
4. Continue until the goal is achieved
5. Set needsMoreWork: false when complete`

/**
 * Constructs the full system prompt by combining base prompt, tool information, and user guidelines
 */
export function constructSystemPrompt(
  availableTools: Array<{ name: string; description: string; inputSchema?: any }>,
  userGuidelines?: string,
  isAgentMode: boolean = false,
  relevantTools?: Array<{ name: string; description: string; inputSchema?: any }>
): string {
  let prompt = BASE_SYSTEM_PROMPT

  if (isAgentMode) {
    prompt += AGENT_MODE_ADDITIONS
  }

  // Helper function to format tool information
  const formatToolInfo = (tools: Array<{ name: string; description: string; inputSchema?: any }>) => {
    return tools.map(tool => {
      let info = `- ${tool.name}: ${tool.description}`
      if (tool.inputSchema?.properties) {
        const params = Object.entries(tool.inputSchema.properties)
          .map(([key, schema]: [string, any]) => {
            const type = schema.type || 'any'
            const required = tool.inputSchema.required?.includes(key) ? ' (required)' : ''
            return `${key}: ${type}${required}`
          })
          .join(', ')
        if (params) {
          info += `\n  Parameters: {${params}}`
        }
      }
      return info
    }).join('\n')
  }

  // Add available tools
  if (availableTools.length > 0) {
    prompt += `\n\nAVAILABLE TOOLS:\n${formatToolInfo(availableTools)}`

    // Add relevant tools section if provided and different from all tools
    if (relevantTools && relevantTools.length > 0 && relevantTools.length < availableTools.length) {
      prompt += `\n\nMOST RELEVANT TOOLS FOR THIS REQUEST:\n${formatToolInfo(relevantTools)}`
    }
  } else {
    prompt += `\n\nNo tools are currently available.`
  }

  // Add user guidelines if provided
  if (userGuidelines?.trim()) {
    prompt += `\n\nADDITIONAL GUIDELINES:\n${userGuidelines.trim()}`
  }

  prompt += `\n\nRespond with ONLY the JSON object, no markdown formatting, no code blocks, no additional text.`

  return prompt
}
